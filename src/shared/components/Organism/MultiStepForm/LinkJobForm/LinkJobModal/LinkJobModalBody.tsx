import Flex from 'shared/uikit/Flex';
import type { FC } from 'react';
import type { IJob<PERSON><PERSON> } from 'shared/types/job';
import Typography from '@shared/uikit/Typography';
import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import Icon from '@shared/uikit/Icon';
import Avatar from '@shared/uikit/Avatar';
import LinkJobModalJobsList from './LinkJobModalBody/LinkJobModalJobsList';
import classes from './LinkJobModal.module.scss';

export interface LinkJobModalBodyCommonProps {
  listData: IJobApi[];
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  fetchNextPage: VoidFunction;
  isLoading: boolean;
  onChangeSearch: (text: string) => void;
}

interface LinkJobModalBodyProps extends LinkJobModalBodyCommonProps {
  step: number;
  isFetchingNextPage?: boolean;
}

const LinkJobModalBody: FC<LinkJobModalBodyProps> = (props) => {
  const { step, isLoading, isFetchingNextPage, ...rest } = props;
  const { data } = useMultiStepFormState('linkJobForm');

  return (
    <Flex className={classes.gap}>
      {data?.cardProps && (
        <Flex
          flexDir="row"
          className={classes.infoCardWrapper}
          alignItems="center"
        >
          {data.cardProps.image && (
            <Avatar
              imgSrc={data.cardProps.image}
              name={data.cardProps.title}
              nameAsPlaceholder
              className={classes.avatar}
              size="smd"
            />
          )}
          {!data.cardProps.image && data.cardProps.icon && (
            <Flex className={classes.avatarInnerWrapper}>
              <Icon
                name={data.cardProps.icon}
                color="smoke_coal"
                type="far"
                size={20}
              />
            </Flex>
          )}
          <Flex className={classes.smallGap}>
            <Typography
              size={15}
              height={18}
              font="700"
              color="smoke_coal"
              ml={8}
            >
              {data.cardProps.title}
            </Typography>
            <Typography size={12} height={18} font="400" color="gray" ml={8}>
              {data.cardProps.text}
            </Typography>
          </Flex>
        </Flex>
      )}
      <LinkJobModalJobsList
        {...rest}
        isLoading={isLoading}
        isFetchingNextPage={isFetchingNextPage}
      />
    </Flex>
  );
};

export default LinkJobModalBody;
