@import '/src/shared/theme/theme.scss';

@layer organism {
  .root {
    gap: variables(largeGutter);
    flex: 1;
    background-color: red !important;
    .jobs {
      gap: variables(xLargeGutter) * 0.5;
      display: flex;
      flex-direction: column;
      -ms-overflow-style: none;
      scrollbar-width: none;
      overflow-y: auto;
      margin: - variables(gutter) * 0.5;
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  .skeletonsRoot {
    gap: variables(largeGutter);
    padding: variables(gutter) * 0.5;

    .searchSkeleton {
      width: 100%;
      height: variables(largeGutter) * 2;
      border-radius: 50px;
      margin-bottom: variables(gutter) * 0.5;
    }
    .skeletonTitleBox {
      justify-content: space-between;
      height: 100%;
      .skeletontitle {
        width: 120px;
        height: variables(gutter);
      }
      .skeletonDesc {
        width: variables(largeGutter) * 2;
        height: variables(gutter);
      }
    }
    .skeletonCheckbox {
      width: variables(xLargeGutter);
      height: variables(xLargeGutter);
      margin-left: auto;
    }
  }

  .itemRoot {
    flex-direction: row;
    gap: variables(gutter);
    align-items: center;
    .skeletonAvatar {
      width: variables(largeGutter) * 2;
      height: variables(largeGutter) * 2;
      border-radius: variables(gutter) * 0.25;
    }
  }
  .checkbox {
    margin-left: auto;
  }
}
