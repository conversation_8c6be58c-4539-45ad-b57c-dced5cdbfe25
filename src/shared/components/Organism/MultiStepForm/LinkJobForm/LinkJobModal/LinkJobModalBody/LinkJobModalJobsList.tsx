import type { FC } from 'react';
import ViewPortList from 'shared/uikit/ViewPortList';
import React, { useCallback } from 'react';
import Flex from 'shared/uikit/Flex';
import type { I<PERSON>ob<PERSON><PERSON> } from 'shared/types/job';
import SearchInputV2 from 'shared/uikit/SearchInputV2';
import { useFormikContext } from 'formik';
import { debounce } from 'lodash';
import useTranslation from '@shared/utils/hooks/useTranslation';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import type { CandidateFormData } from '@shared/types/candidates';
import classes from './LinkJobModalJobsList.module.scss';
import type { LinkJobModalBodyCommonProps } from '../LinkJobModalBody';
import LinkJobModalJobsListFooter from './LinkJobModalJobsList/LinkJobModalJobsListFooter';
import LinkJobModalJobItem from './LinkJobModalJobsList/LinkJobModalJobItem';
import LinkJobModalCandidateItem from './LinkJobModalJobsList/LinkJobModalCandidateItem';

const LinkJobModalJobsList: FC<LinkJobModalBodyCommonProps> = ({
  listData,
  hasNextPage,
  fetchNextPage,
  isFetchingNextPage,
  isLoading,
  onChangeSearch,
}) => {
  const { t } = useTranslation();
  const { data } = useMultiStepFormState('linkJobForm');
  const { values, setFieldValue } = useFormikContext() as {
    values: { jobs: IJobApi[] };
    setFieldValue: (name: string, data: IJobApi[]) => void;
  };
  const debounceFn = useCallback(
    debounce((text: string) => onChangeSearch(text), 500),
    []
  );

  const onClickCheckBox = (item: IJobApi) => {
    const list: IJobApi[] = values.jobs;
    const checked = list.some((_item) => _item.id === item.id);
    if (checked) {
      setFieldValue(
        'jobs',
        list.filter((job) => job.id !== item.id)
      );
    } else {
      setFieldValue('jobs', [...list, item]);
    }
  };
  const onAddOrRemoveCandidate = (
    candidate: CandidateFormData,
    action: 'add' | 'remove'
  ) => {
    const list = values.jobs as any[];

    if (action === 'add') {
      setFieldValue('jobs', [...list, candidate]);
    } else
      setFieldValue(
        'jobs',
        list.filter((_candidate) => _candidate.id !== candidate.id)
      );
  };

  const Item = useCallback(
    (index: number, job: any) => {
      if (data?.target === 'job')
        return (
          <LinkJobModalCandidateItem
            candidate={job}
            onClick={onAddOrRemoveCandidate}
            selectedCandidates={values.jobs as any[]}
          />
        );
      return (
        <LinkJobModalJobItem
          job={job}
          onClick={onClickCheckBox}
          selectedJobs={values.jobs}
        />
      );
    },
    [values.jobs, data?.target]
  );

  const Footer = useCallback(
    () => (hasNextPage || isLoading ? <LinkJobModalJobsListFooter /> : null),
    [hasNextPage, isLoading]
  );

  return (
    <Flex className={classes.root}>
      <SearchInputV2
        onChange={(e) => debounceFn(e)}
        placeholder={t(placeholderText[data?.target ?? 'project'])}
      />
      {isLoading || listData?.length ? (
        <ViewPortList
          useRelativeScroller
          style={{ height: '100%' }}
          data={listData}
          endReached={() => {
            if (hasNextPage && !isFetchingNextPage) {
              fetchNextPage();
            }
          }}
          atBottomThreshold={100}
          increaseViewportBy={50}
          itemContent={Item}
          className={classes.jobs}
          components={{
            List: Flex,
            Footer,
          }}
        />
      ) : (
        <EmptySearchResult
          title={t('no_result_found')}
          sectionMessage={t('try_refining_search')}
          className="flex-1"
        />
      )}
    </Flex>
  );
};

export default LinkJobModalJobsList;

const placeholderText = {
  project: 'search_jobs',
  candidate: 'search_jobs',
  job: 'search_candidates',
};
